package middleware

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"stellar-go/internal/config"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestCORSMiddleware(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name           string
		corsConfig     config.CORSConfig
		origin         string
		method         string
		expectedOrigin string
		expectedStatus int
	}{
		{
			name: "Allowed origin",
			corsConfig: config.CORSConfig{
				AllowedOrigins:   "http://localhost:3000,http://localhost:3001",
				AllowCredentials: true,
				AllowedMethods:   "GET,POST,PUT,DELETE,OPTIONS",
				AllowedHeaders:   "Content-Type,Authorization",
			},
			origin:         "http://localhost:3000",
			method:         "GET",
			expectedOrigin: "http://localhost:3000",
			expectedStatus: http.StatusOK,
		},
		{
			name: "Another allowed origin",
			corsConfig: config.CORSConfig{
				AllowedOrigins:   "http://localhost:3000,http://localhost:3001",
				AllowCredentials: true,
				AllowedMethods:   "GET,POST,PUT,DELETE,OPTIONS",
				AllowedHeaders:   "Content-Type,Authorization",
			},
			origin:         "http://localhost:3001",
			method:         "GET",
			expectedOrigin: "http://localhost:3001",
			expectedStatus: http.StatusOK,
		},
		{
			name: "Wildcard origin",
			corsConfig: config.CORSConfig{
				AllowedOrigins:   "*",
				AllowCredentials: false,
				AllowedMethods:   "GET,POST,PUT,DELETE,OPTIONS",
				AllowedHeaders:   "Content-Type,Authorization",
			},
			origin:         "http://example.com",
			method:         "GET",
			expectedOrigin: "http://example.com",
			expectedStatus: http.StatusOK,
		},
		{
			name: "OPTIONS request",
			corsConfig: config.CORSConfig{
				AllowedOrigins:   "http://localhost:3000",
				AllowCredentials: true,
				AllowedMethods:   "GET,POST,PUT,DELETE,OPTIONS",
				AllowedHeaders:   "Content-Type,Authorization",
			},
			origin:         "http://localhost:3000",
			method:         "OPTIONS",
			expectedOrigin: "http://localhost:3000",
			expectedStatus: http.StatusNoContent,
		},
		{
			name: "Disallowed origin",
			corsConfig: config.CORSConfig{
				AllowedOrigins:   "http://localhost:3000",
				AllowCredentials: true,
				AllowedMethods:   "GET,POST,PUT,DELETE,OPTIONS",
				AllowedHeaders:   "Content-Type,Authorization",
			},
			origin:         "http://malicious.com",
			method:         "GET",
			expectedOrigin: "",
			expectedStatus: http.StatusOK,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create test config
			cfg := &config.Config{
				CORS: tt.corsConfig,
			}

			// Create test router
			router := gin.New()
			router.Use(CORSMiddleware(cfg))
			router.GET("/test", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{"message": "test"})
			})

			// Create test request
			req := httptest.NewRequest(tt.method, "/test", nil)
			if tt.origin != "" {
				req.Header.Set("Origin", tt.origin)
			}

			// Create response recorder
			w := httptest.NewRecorder()

			// Perform request
			router.ServeHTTP(w, req)

			// Check status code
			assert.Equal(t, tt.expectedStatus, w.Code)

			// Check CORS headers
			if tt.expectedOrigin != "" {
				assert.Equal(t, tt.expectedOrigin, w.Header().Get("Access-Control-Allow-Origin"))
			} else {
				assert.Empty(t, w.Header().Get("Access-Control-Allow-Origin"))
			}

			// Check other CORS headers
			assert.Equal(t, tt.corsConfig.AllowedMethods, w.Header().Get("Access-Control-Allow-Methods"))
			assert.Equal(t, tt.corsConfig.AllowedHeaders, w.Header().Get("Access-Control-Allow-Headers"))

			if tt.corsConfig.AllowCredentials {
				assert.Equal(t, "true", w.Header().Get("Access-Control-Allow-Credentials"))
			}
		})
	}
}
